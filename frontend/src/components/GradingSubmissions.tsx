import React, { useCallback, useEffect, useState } from 'react';
import { Status, StatusUtils, TestSubmission } from '../types/aegisGrader';
import { useNavigate } from 'react-router-dom';
import { useAxiosPrivate } from "@/hooks/useAxiosPrivate";
import { fetchWithCache } from '@/utils/cacheUtil';
import { useMobileInteractions } from '@/hooks/useMobileInteractions';
import { toast } from 'react-toastify';
import { AlertCircle } from 'lucide-react';
import { useUser } from '@/contexts/userContext';
import PulsatingDots from './PulsatingDotsLoader';

// --- Simplified Status Badge ---
const StatusBadge: React.FC<{ status?: Status | string }> = React.memo(({ status }) => {
    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${StatusUtils.getStatusClasses(status)}`}>
            {StatusUtils.getDisplayText(status)}
        </span>
    );
});

export const GradingSubmissions: React.FC = () => {
    const navigate = useNavigate();
    const [testHistory, setTestHistory] = useState<TestSubmission[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();

    // Mobile interactions
    const { handleButtonPress } = useMobileInteractions({
        enableSwipe: true,
        swipeThreshold: 60
    });
    
    useEffect(() => {
        const fetchGradingSubmissions = async () => {
            try {
                setIsLoading(true);
                const data = await fetchWithCache(axiosPrivate, `/api/aegisGrader/submissions/${user?.id}`);
                console.log("Fetched submissions:", data);
                setTestHistory(data.submissions || []);
            } catch (error) {
                console.error("Error fetching submissions:", error);
            } finally {
                setIsLoading(false);
            }
        }
        fetchGradingSubmissions();
    }, [user]);

    const viewGradingDetails = useCallback((id: string | undefined) => {
        if (!id) {
            toast.error("Invalid submission ID");
            return;
        }

        const submission = testHistory.find(sub => sub.id === id);
        if (!submission) {
            toast.error("Submission not found");
            return;
        }

        navigate(`/gradingDetails/` + id, {
            state: { testHistory },
        });
    }, [navigate, testHistory]);

    return (
        <div className="bg-card rounded-xl shadow-sm border border-border h-full flex flex-col">
            {/* Content Container - Scrollable */}
            <div className="flex-1 overflow-y-auto p-2">
                <div className="flex flex-col gap-2">
                    {isLoading ? (
                        <div className="text-center text-muted-foreground py-8 sm:py-12">
                            <div className="flex flex-col items-center gap-3">
                                <PulsatingDots />
                                <div>
                                    <h3 className="text-base sm:text-lg font-medium text-foreground mb-1">Loading submissions...</h3>
                                    <p className="text-sm text-muted-foreground">Please wait while we fetch your grading submissions</p>
                                </div>
                            </div>
                        </div>
                    ) : testHistory.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8 sm:py-12">
                            <div className="flex flex-col items-center gap-3">
                                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-muted rounded-full flex items-center justify-center">
                                    <AlertCircle className="w-8 h-8 sm:w-10 sm:h-10 text-muted-foreground" />
                                </div>
                                <div>
                                    <h3 className="text-base sm:text-lg font-medium text-foreground mb-1">No submissions yet</h3>
                                    <p className="text-sm text-muted-foreground">Your test grading submissions will appear here</p>
                                </div>
                            </div>
                        </div>
                    ) : (
                        testHistory.map((submission, index) => (
                            <div
                                key={index}
                                className="border rounded-xl p-4 sm:p-6 hover:bg-muted/50 cursor-pointer transition-all duration-200 hover:shadow-md touch-manipulation mobile-button"
                                onClick={handleButtonPress(() => viewGradingDetails(submission.id || ''))}
                            >
                                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3">
                                    <div className="flex-1 min-w-0">
                                        {/* Subject and Badge Row - Modified for mobile */}
                                        <div className="flex items-center justify-between gap-2 mb-2">
                                            <h3 className="text-base sm:text-lg font-semibold text-foreground truncate flex-1">
                                                {submission.testDetails.subject}
                                            </h3>
                                            {/* Badge positioned on the right for both mobile and desktop */}
                                            <StatusBadge status={submission.status || undefined} />
                                        </div>
                                        <p className="text-sm sm:text-base text-muted-foreground">
                                            {submission.testDetails.className} • {new Date(submission.testDetails.date).toLocaleDateString()}
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground sm:mt-0">
                                        <span className="bg-muted px-2 py-1 rounded-md">
                                            {submission.answerSheets.length} sheet{submission.answerSheets.length !== 1 ? 's' : ''}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
}

export default GradingSubmissions;
